"use client";

import React from "react";

export function Gallery10() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28 bg-background-primary">
      <div className="container">
        <div className="mb-12 text-center md:mb-18 lg:mb-20">
          <h2 className="mb-5 text-5xl font-semibold md:mb-6 md:text-7xl lg:text-8xl">
            Moodboard
          </h2>
        </div>
        <div className="gap-8 space-y-8 md:columns-3">
          <a href="#" className="block w-full ">
            <img
              src="/images/algemeen/moodboard 1.png"
              alt="Relume placeholder image 1"
              className="size-full rounded-image object-cover rounded-lg"
            />
          </a>
          <a href="#" className="block w-full">
            <img
              src="/images/algemeen/moodboard 2.png"
              alt="Relume placeholder image 2"
              className="size-full rounded-image object-cover rounded-lg"
            />
          </a>
          <a href="#" className="block w-full">
            <img
              src="/images/algemeen/moodboard 3.png"
              alt="Relume placeholder image 3"
              className="size-full rounded-image object-cover rounded-lg"
            />
          </a>
          <a href="#" className="block w-full">
            <img
              src="/images/algemeen/moodboard 4.png"
              alt="Relume placeholder image 4"
              className="size-full rounded-image object-cover rounded-lg"
            />
          </a>
          <a href="#" className="block w-full">
            <img
              src="/images/algemeen/moodboard 2.png"
              alt="Relume placeholder image 5"
              className="size-full rounded-image object-cover rounded-lg"
            />
          </a>
          <a href="#" className="block w-full">
            <img
              src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
              alt="Relume placeholder image 6"
              className="size-full rounded-image object-cover rounded-lg"
            />
          </a>
          <a href="#" className="block w-full">
            <img
              src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
              alt="Relume placeholder image 7"
              className="size-full rounded-image object-cover rounded-lg"
            />
          </a>
        </div>
      </div>
    </section>
  );
}
