"use client";

import React, { useState } from "react";

export function Gallery10() {
  const [selectedImage, setSelectedImage] = useState(null);

  const images = [
    {
      src: "/images/algemeen/moodboard 1.png",
      alt: "Relume placeholder image 1"
    },
    {
      src: "/images/algemeen/moodboard 2.png",
      alt: "Relume placeholder image 2"
    },
    {
      src: "/images/algemeen/moodboard 3.png",
      alt: "Relume placeholder image 3"
    },
    {
      src: "/images/algemeen/moodboard 4.png",
      alt: "Relume placeholder image 4"
    },
    {
      src: "/images/algemeen/moodboard 5.png",
      alt: "Relume placeholder image 5"
    },
    {
      src: "/images/algemeen/moodboard 6.png",
      alt: "Relume placeholder image 6"
    },
    {
      src: "/images/algemeen/moodboard 7.png",
      alt: "Relume placeholder image 7"
    }
  ];

  const openModal = (image) => {
    setSelectedImage(image);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28 bg-background-primary">
      <div className="container">
        <div className="mb-12 text-center md:mb-18 lg:mb-20">
          <h2 className="mb-5 text-5xl font-semibold md:mb-6 md:text-7xl lg:text-8xl">
            Moodboard
          </h2>
        </div>
        <div className="gap-8 space-y-8 md:columns-3">
          {images.map((image, index) => (
            <button
              key={index}
              onClick={() => openModal(image)}
              className="block w-full cursor-pointer overflow-hidden rounded-lg transition-transform duration-300 ease-in-out hover:scale-105"
            >
              <img
                src={image.src}
                alt={image.alt}
                className="size-full rounded-image object-cover rounded-lg transition-transform duration-300 ease-in-out"
              />
            </button>
          ))}
        </div>
      </div>

      {/* Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 p-4"
          onClick={closeModal}
        >
          <div className="relative max-h-[90vh] max-w-[90vw]">
            <button
              onClick={closeModal}
              className="absolute -top-10 right-0 text-white hover:text-gray-300 transition-colors duration-200"
              aria-label="Close modal"
            >
              <svg
                className="h-8 w-8"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
            <img
              src={selectedImage.src}
              alt={selectedImage.alt}
              className="max-h-full max-w-full object-contain rounded-lg"
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        </div>
      )}
    </section>
  );
}
