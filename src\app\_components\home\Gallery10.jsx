"use client";

import React, { useState } from "react";

export function Gallery10() {
  const [selectedImage, setSelectedImage] = useState(null);

  const images = [
    {
      src: "/images/algemeen/moodboard 1.png",
      alt: "Relume placeholder image 1"
    },
    {
      src: "/images/algemeen/moodboard 2.png",
      alt: "Relume placeholder image 2"
    },
    {
      src: "/images/algemeen/moodboard 3.png",
      alt: "Relume placeholder image 3"
    },
    {
      src: "/images/algemeen/moodboard 4.png",
      alt: "Relume placeholder image 4"
    },
    {
      src: "/images/algemeen/moodboard 5.png",
      alt: "Relume placeholder image 5"
    },
    {
      src: "/images/algemeen/moodboard 6.png",
      alt: "Relume placeholder image 6"
    },
    {
      src: "/images/algemeen/moodboard 7.png",
      alt: "Relume placeholder image 7"
    }
  ];

  const openModal = (image) => {
    setSelectedImage(image);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28 bg-background-primary">
      <div className="container">
        <div className="mb-12 text-center md:mb-18 lg:mb-20">
          <h2 className="mb-5 text-5xl font-semibold md:mb-6 md:text-7xl lg:text-8xl">
            Moodboard
          </h2>
        </div>
        <div className="gap-8 space-y-8 md:columns-3">
          {images.map((image, index) => (
            <button
              key={index}
              onClick={() => openModal(image)}
              className="block w-full cursor-pointer overflow-hidden rounded-lg transition-transform duration-300 ease-in-out hover:scale-105"
            >
              <img
                src={image.src}
                alt={image.alt}
                className="size-full rounded-image object-cover rounded-lg transition-transform duration-300 ease-in-out"
              />
            </button>
          ))}
        </div>
      </div>

      {/* Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/80 backdrop-blur-sm p-2 sm:p-4 animate-in fade-in duration-300"
          onClick={closeModal}
        >
          <div className="relative flex items-center justify-center max-h-[95vh] max-w-[95vw] sm:max-h-[90vh] sm:max-w-[90vw] md:max-h-[85vh] md:max-w-[85vw] animate-in zoom-in-95 duration-300">
            {/* Close button */}
            <button
              onClick={closeModal}
              className="absolute top-2 right-2 sm:top-4 sm:right-4 z-10 flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-full bg-white/10 text-white backdrop-blur-md transition-all duration-200 hover:bg-white/20 hover:scale-110"
              aria-label="Close modal"
            >
              <svg
                className="h-4 w-4 sm:h-6 sm:w-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>

            {/* Image container with enhanced styling */}
            <div className="relative p-2 sm:p-4 md:p-6 lg:p-8">
              <img
                src={selectedImage.src}
                alt={selectedImage.alt}
                className="max-h-full max-w-full object-contain rounded-lg sm:rounded-xl"
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          </div>
        </div>
      )}
    </section>
  );
}
